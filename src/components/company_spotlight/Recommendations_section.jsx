import recommend from '@algolia/recommend';
import { RelatedProducts } from '@algolia/recommend-react';
import { HorizontalSlider } from '@algolia/ui-components-horizontal-slider-react';
import '@algolia/ui-components-horizontal-slider-theme';
import React from 'react';
import EquipperSpotlightEquipmentsItem from '../../shared/components/equipment/Equipper_spotlight_equipments_item';
import citiesData from '../../shared/helpers/Cities.json';

const recommendClient = recommend(
  `${import.meta.env.VITE_ALGOLIA_ID}`,
  `${import.meta.env.VITE_ALGOLIA_API_KEY}`
);

// Function to generate filter for non-US locations
function generateNonUSFilter() {
  const nonUSLocations = [];

  citiesData.forEach((city) => {
    const { value } = city;

    // Check if it's a Canadian location (contains Ontario or Quebec)
    if (value.includes(', Ontario') || value.includes(', Quebec')) {
      nonUSLocations.push(`"${value}"`);
    }

    // Check if it's an Arab country (format: "Country - City")
    if (
      value.includes('SA - ') ||
      value.includes('UAE - ') ||
      value.includes('Qatar - ') ||
      value.includes('Oman - ')
    ) {
      nonUSLocations.push(`"${value}"`);
    }
  });

  // Create the filter string using AND NOT for each location
  const filterParts = nonUSLocations.map(
    (location) => `NOT coverage_area:${location}`
  );
  const filterString = filterParts.join(' AND ');
  // Senior log for debugging filter string
  // eslint-disable-next-line no-console
  console.log('[RecommendationsSection] Algolia filter string:', filterString);
  return filterString;
}

function ItemRelated({ item }) {
  // Senior log for debugging each recommended item
  // eslint-disable-next-line no-console
  console.log('[RecommendationsSection] Rendering recommended item:', item);
  return (
    <EquipperSpotlightEquipmentsItem hit={item} isRecommendationsSection />
  );
}

export default function RecommendationsSection({ objectID }) {
  // Senior log for debugging objectID
  // eslint-disable-next-line no-console
  console.log('[RecommendationsSection] objectID:', objectID);

  if (objectID === '' || !objectID) {
    // eslint-disable-next-line no-console
    console.warn(
      '[RecommendationsSection] No objectID provided, returning null'
    );
    return null;
  }
  return (
    <RelatedProducts
      recommendClient={recommendClient}
      indexName={import.meta.env.VITE_ALGOLIA_INDEX_NAME}
      objectIDs={[objectID]}
      itemComponent={ItemRelated}
      view={HorizontalSlider}
      maxRecommendations={8}
      translations={{
        title: ' '
      }}
      queryParameters={{
        filters: generateNonUSFilter()
      }}
    />
  );
}
