// import ACQ from '../../style/assets/img/partnering_with/ACQ.png';
// import CRA from '../../style/assets/img/partnering_with/CRA.png';
import WheelJackets from '../../style/assets/img/partnering_with/WheelJackets.png';
import Fero from '../../style/assets/img/partnering_with/FeroLogo.jpeg';
import Biolift from '../../style/assets/img/partnering_with/Logo-Biolift.png';
import Ganahl from '../../style/assets/img/partnering_with/ganahl-logo.png';
import FundingYourWay from '../../style/assets/img/partnering_with/FundingYourWay.png';
import CitationLogistics from '../../style/assets/img/partnering_with/CitationLogistics.png';

import { firstLetterUpperCase } from './String_helps';
import { convertDate } from './Date_helper';

export const paymentMethod = [
  {
    id: 'credit_card',
    name: 'payment',
    defaultChecked: true,
    label: 'Credit_card'
  },
  {
    id: 'credit_account',
    name: 'payment',
    label: 'Credit_account'
  }
];

export const insurance = {
  data: [
    {
      name: 'insurance.insurance_company',
      placeholder: 'Insurance_company',
      type: 'text'
    },
    {
      name: 'insurance.insurance_policy_number',
      placeholder: 'Insurance_policy_number',
      type: 'text'
    },
    {
      name: 'insurance.insurance_coverage',
      placeholder: 'Insurance_coverage',
      type: 'text'
    },
    {
      name: 'insurance.expiry_date_of_insurance',
      placeholder: 'Expiry_date_of_insurance',
      type: 'date'
    }
  ]
};

export const priceData = (data, t) => [
  {
    placeholder: t('Day'),
    value: data?.price?.day,
    currency: data?.price?.currency,
    name: 'day'
  },
  {
    placeholder: t('Week'),
    value: data?.price?.week,
    currency: data?.price?.currency,
    name: 'week'
  },
  {
    placeholder: t('Four_week'),
    value: data?.price?.month,
    currency: data?.price?.currency,
    name: 'month'
  }
];

export const equipmentsDetails = (t, data) => [
  {
    value1: {
      label: t('Brand'),
      value: data?.brand
    },
    value2: {
      label: t('Brand_model'),
      value: data?.brand_model
    }
  },
  {
    value1: {
      label: t('Drive_type'),
      value: data?.drive_type?.join(', ')
    },
    value2: {
      label: t('Weight'),
      value: data?.weight
    }
  },
  {
    value1: { label: t('Width'), value: data?.width },
    value2: {
      label: t('Height'),
      value: data?.height
    }
  },
  {
    value1: {
      label: t('Length'),
      value: data?.length
    },
    value2: {
      label: t('Diameter'),
      value: data?.diameter
    }
  },
  {
    value1: {
      label: t('Cut_diameter'),
      value: data?.cut_diameter
    },
    value2: {
      label: t('Force'),
      value: data?.force
    }
  },
  {
    value1: {
      label: t('Btu'),
      value: data?.btu
    },
    value2: {
      label: t('Volt'),
      value: data?.volt
    }
  },
  {
    value1: {
      label: t('Watt'),
      value: data?.watt
    },
    value2: { label: t('CFM'), value: data?.cfm }
  },
  {
    value1: { label: t('Capacity'), value: data?.capacity },
    value2: {
      label: t('Type_of_propulsion'),
      value: data?.type_of_propulsion?.join(', ')
    }
  },
  {
    value1: {
      label: t('Platform_height'),
      value: data?.platform_height
    },
    value2: {
      label: t('Working_height'),
      value: data?.working_height
    }
  },
  {
    value1: {
      label: t('Horizontal_outreach'),
      value: data?.horizontal_outreach
    },
    value2: {
      label: t('Platform_capacity'),
      value: data?.platform_capacity
    }
  },
  {
    value1: {
      label: t('Platform_dimension'),
      value: data?.platform_dimension
    },
    value2: {
      label: t('Platform_extension'),
      value: data?.platform_extension
    }
  },
  {
    value1: {
      label: t('Extension_capacity'),
      value: data?.extension_capacity
    },
    value2: {
      label: t('Platform_rotation'),
      value: data?.platform_rotation
    }
  },
  {
    value1: {
      label: t('Machine_rotation'),
      value: data?.machine_rotation
    },
    value2: {
      label: t('Machine_width'),
      value: data?.machine_width
    }
  },
  {
    value1: {
      label: t('Machine_length'),
      value: data?.machine_length
    },
    value2: {
      label: t('Machine_height'),
      value: data?.machine_height
    }
  },
  {
    value1: {
      label: t('Closed_machine_height'),
      value: data?.closed_machine_height
    },
    value2: {
      label: t('Closed_machine_length'),
      value: data?.closed_machine_length
    }
  },
  {
    value1: {
      label: t('Closed_machine_width'),
      value: data?.closed_machine_width
    },
    value2: {
      label: t('Basket_capacity'),
      value: data?.basket_capacity
    }
  },
  {
    value1: {
      label: t('Basket_length'),
      value: data?.basket_length
    },
    value2: {
      label: t('Basket_width'),
      value: data?.basket_width
    }
  },
  {
    value1: {
      label: t('Legs_location'),
      value: data?.legs_location
    },
    value2: {
      label: t('Floor_height'),
      value: data?.floor_height
    }
  },
  {
    value1: {
      label: t('Cabin_height'),
      value: data?.cabin_height
    },
    value2: {
      label: t('Wheelbase'),
      value: data?.wheelbase
    }
  },
  {
    value1: {
      label: t('Wheel_size'),
      value: data?.wheel_size
    },
    value2: {
      label: t('Plate_dimension'),
      value: data?.plate_dimension
    }
  },
  {
    value1: {
      label: t('Decibel'),
      value: data?.decibel
    },
    value2: {
      label: t('Roll_width'),
      value: data?.roll_width
    }
  },
  {
    value1: {
      label: t('Compaction_equip_spec'),
      value: data?.compaction
    },
    value2: {
      label: t('Vibrations'),
      value: data?.vibrations
    }
  },
  {
    value1: {
      label: t('Lumen'),
      value: data?.lumen
    },
    value2: {
      label: t('Pressure'),
      value: data?.pressure
    }
  },
  {
    value1: {
      label: t('Frequency'),
      value: data?.frequency
    },
    value2: {
      label: t('Tilting_capacity'),
      value: data?.tilting_capacity
    }
  },
  {
    value1: {
      label: t('Operation_capacity'),
      value: data?.operation_capacity
    },
    value2: {
      label: t('Tank_capacity'),
      value: data?.tank_capacity
    }
  },
  {
    value1: {
      label: t('Digging_depth'),
      value: data?.digging_depth
    },
    value2: {
      label: t('Dumping_height'),
      value: data?.dumping_height
    }
  },
  {
    value1: {
      label: t('Digging_radius'),
      value: data?.digging_radius
    },
    value2: {
      label: t('Technical_data_sheet'),
      value: data?.technical_data_sheet
    }
  },
  {
    value1: {
      label: t('Equipment_usages'),
      value: data?.equipment_usages
    }
  },
  {
    value1: { label: t('Consumption'), value: data?.consumption }
  }
];

export const equipmentDetails = (t, data) => [
  {
    value1: {
      label: `${t('Available_from')}:`,
      value: convertDate(new Date(parseInt(data?.available_from)) || null)
    },
    value2: {
      label: `${t('Status')} :`,
      value: firstLetterUpperCase(data?.status)
    }
  },
  {
    value1: {
      label: t('Brand'),
      value: data?.brand
    },
    value2: {
      label: t('Brand_model'),
      value: data?.brand_model
    }
  },
  {
    value1: {
      label: t('Drive_type'),
      value: data?.drive_type?.join(', ')
    },
    value2: {
      label: t('Weight'),
      value: data?.weight
    }
  },
  {
    value1: { label: t('Width'), value: data?.width },
    value2: {
      label: t('Height'),
      value: data?.height
    }
  },
  {
    value1: {
      label: t('Length'),
      value: data?.length
    },
    value2: {
      label: t('Diameter'),
      value: data?.diameter
    }
  },
  {
    value1: {
      label: t('Cut_diameter'),
      value: data?.cut_diameter
    },
    value2: {
      label: t('Force'),
      value: data?.force
    }
  },
  {
    value1: {
      label: t('Btu'),
      value: data?.btu
    },
    value2: {
      label: t('Volt'),
      value: data?.volt
    }
  },
  {
    value1: {
      label: t('Watt'),
      value: data?.watt
    },
    value2: { label: t('CFM'), value: data?.cfm }
  },
  {
    value1: { label: t('Capacity'), value: data?.capacity },
    value2: {
      label: t('Type_of_propulsion'),
      value: data?.type_of_propulsion?.join(', ')
    }
  },
  {
    value1: {
      label: t('Platform_height'),
      value: data?.platform_height
    },
    value2: {
      label: t('Working_height'),
      value: data?.working_height
    }
  },
  {
    value1: {
      label: t('Horizontal_outreach'),
      value: data?.horizontal_outreach
    },
    value2: {
      label: t('Platform_capacity'),
      value: data?.platform_capacity
    }
  },
  {
    value1: {
      label: t('Platform_dimension'),
      value: data?.platform_dimension
    },
    value2: {
      label: t('Platform_extension'),
      value: data?.platform_extension
    }
  },
  {
    value1: {
      label: t('Extension_capacity'),
      value: data?.extension_capacity
    },
    value2: {
      label: t('Platform_rotation'),
      value: data?.platform_rotation
    }
  },
  {
    value1: {
      label: t('Machine_rotation'),
      value: data?.machine_rotation
    },
    value2: {
      label: t('Machine_width'),
      value: data?.machine_width
    }
  },
  {
    value1: {
      label: t('Machine_length'),
      value: data?.machine_length
    },
    value2: {
      label: t('Machine_height'),
      value: data?.machine_height
    }
  },
  {
    value1: {
      label: t('Closed_machine_height'),
      value: data?.closed_machine_height
    },
    value2: {
      label: t('Closed_machine_length'),
      value: data?.closed_machine_length
    }
  },
  {
    value1: {
      label: t('Closed_machine_width'),
      value: data?.closed_machine_width
    },
    value2: {
      label: t('Basket_capacity'),
      value: data?.basket_capacity
    }
  },
  {
    value1: {
      label: t('Basket_length'),
      value: data?.basket_length
    },
    value2: {
      label: t('Basket_width'),
      value: data?.basket_width
    }
  },
  {
    value1: {
      label: t('Legs_location'),
      value: data?.legs_location
    },
    value2: {
      label: t('Floor_height'),
      value: data?.floor_height
    }
  },
  {
    value1: {
      label: t('Cabin_height'),
      value: data?.cabin_height
    },
    value2: {
      label: t('Wheelbase'),
      value: data?.wheelbase
    }
  },
  {
    value1: {
      label: t('Wheel_size'),
      value: data?.wheel_size
    },
    value2: {
      label: t('Plate_dimension'),
      value: data?.plate_dimension
    }
  },
  {
    value1: {
      label: t('Decibel'),
      value: data?.decibel
    },
    value2: {
      label: t('Roll_width'),
      value: data?.roll_width
    }
  },
  {
    value1: {
      label: t('Compaction_equip_spec'),
      value: data?.compaction
    },
    value2: {
      label: t('Vibrations'),
      value: data?.vibrations
    }
  },
  {
    value1: {
      label: t('Lumen'),
      value: data?.lumen
    },
    value2: {
      label: t('Pressure'),
      value: data?.pressure
    }
  },
  {
    value1: {
      label: t('Frequency'),
      value: data?.frequency
    },
    value2: {
      label: t('Tilting_capacity'),
      value: data?.tilting_capacity
    }
  },
  {
    value1: {
      label: t('Operation_capacity'),
      value: data?.operation_capacity
    },
    value2: {
      label: t('Tank_capacity'),
      value: data?.tank_capacity
    }
  },
  {
    value1: {
      label: t('Digging_depth'),
      value: data?.digging_depth
    },
    value2: {
      label: t('Dumping_height'),
      value: data?.dumping_height
    }
  },
  {
    value1: {
      label: t('Digging_radius'),
      value: data?.digging_radius
    },
    value2: {
      label: t('Technical_data_sheet'),
      value: data?.technical_data_sheet
    }
  },
  {
    value1: {
      label: t('Equipment_usages'),
      value: data?.equipment_usages
    }
  },
  {
    value1: { label: t('Consumption'), value: data?.consumption }
  }
];

export const calculatePrice = (data, startDate, endDate, hasInsurance) => {
  const {
    price: { day, week, month },
    discount
  } = data;

  const duration = calculateDurationInDays(startDate, endDate);

  const subTotal = calculateSubTotal(duration, day, week, month);
  // const serviceFees = calculateServiceFees(subTotal);
  const waiverInsurance = calculateWaiverInsurance(subTotal, hasInsurance);
  const totalAmount = calculateTotalAmount(subTotal, 0, waiverInsurance);

  return {
    sub_total: subTotal,
    service_fees: 0,
    waiver_insurance: waiverInsurance,
    discount: discount,
    amount: totalAmount,
    total_amount: discount
      ? totalAmount - (totalAmount * discount) / 100
      : totalAmount
  };
};

const calculateSubTotal = (duration, day, week, month) => {
  const days = duration % 7;
  const weeks = Math.floor(duration / 7) % 4;
  const fourWeeks = Math.floor(duration / 28);

  return days * day + weeks * week + fourWeeks * month;
};

// const calculateServiceFees = (subTotal) => subTotal * 0.03;

const calculateWaiverInsurance = (subTotal, hasInsurance) =>
  hasInsurance ? 0 : subTotal * 0.15;

const calculateTotalAmount = (subTotal, serviceFees, waiverInsurance) =>
  subTotal + serviceFees + waiverInsurance;

function formatCurrency(numberString) {
  const formattedNumber = numberString?.toLocaleString('en-US', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  });

  return formattedNumber;
}

export const pricesDetails = (priceDetails, t) => {
  const totalAmount = priceDetails?.amount;
  const discount = (
    (priceDetails?.sub_total * priceDetails?.discount * 100) /
    100
  ).toFixed(2);

  return [
    {
      label: `${t('Sub_total')}`,
      name: 'sub_total',
      value: formatCurrency((priceDetails?.sub_total * 100) / 100)
    },
    {
      label: `${t('Service_fees')}`,
      name: 'service_fees',
      value: formatCurrency((priceDetails?.service_fees * 100) / 100)
    },
    {
      label: `${t('Discount')} (${priceDetails?.discount * 100 || 0} %)`,
      name: 'discount',
      value: priceDetails?.discount
        ? priceDetails.discount_amount
          ? formatCurrency(priceDetails.discount_amount)
          : `- ${discount}`
        : 0
    },
    {
      label: `${t('Assurance_renonciation_dommage_us')}`,
      name: 'assurance',
      value: formatCurrency((priceDetails?.waiver_insurance * 100) / 100)
    },
    {
      label: `${t('Tax')}`,
      name: 'tax',
      value: formatCurrency(priceDetails?.tax) || 0
    },
    {
      label: `${t('This_total_amount_excludes_tax')}`,
      name: 'info',
      value: ''
    },
    {
      label: `${t('Total_amount')}`,
      name: 'total_amount',
      value: priceDetails.paid_amount
        ? formatCurrency(priceDetails.paid_amount)
        : formatCurrency(totalAmount - discount)
    }
  ];
};
export const itemsToReturn = (value) => {
  return ![
    'available_from',
    'address',
    '__position',
    '_highlightResult',
    'objectID',
    'lastmodified',
    'coverage_area',
    'equipper_id',
    'total_comments',
    'avg_rating',
    'status',
    'category',
    'image_link',
    'path',
    'name',
    'price',
    'sub_category',
    'name_fr',
    'name_en',
    'id',
    'requested_name',
    'description',
    'equipment_utility',
    'alias_id',
    'description_fr',
    'alias.fr',
    'alias.en',
    'internal_id',
    'serial_number',
    'is_active',
    'equipper_equipment_picture'
  ].includes(value);
};

export const itemsBidzToReturn = (value) => {
  return ![
    'category',
    'sub_category',
    '__position',
    'objectID',
    'lastmodified',
    'path'
  ].includes(value);
};

export const equipmentsInitialValues = {
  type_of_propulsion: '',
  weight: '',
  height: '',
  width: '',
  length: '',
  force: '',
  btu: '',
  volt: '',
  kw: '',
  cfm: '',
  consumption: '',
  capacity: '',
  description: '',
  equipment_utility: ''
};

export const days = [
  {
    day_of_week: 'Monday',
    open: '04:00',
    close: '20:00',
    day_off: false,
    index: 0
  },
  {
    day_of_week: 'Tuesday',
    open: '04:00',
    close: '20:00',
    day_off: false,
    index: 1
  },
  {
    day_of_week: 'Wednesday',
    open: '04:00',
    close: '20:00',
    day_off: false,
    index: 2
  },
  {
    day_of_week: 'Thursday',
    open: '04:00',
    close: '20:00',
    day_off: false,
    index: 3
  },
  {
    day_of_week: 'Friday',
    open: '04:00',
    close: '20:00',
    day_off: false,
    index: 4
  },
  {
    day_of_week: 'Saturday',
    open: '04:00',
    close: '20:00',
    day_off: false,
    index: 5
  },
  {
    day_of_week: 'Sunday',
    open: '04:00',
    close: '20:00',
    day_off: false,
    index: 6
  }
];

export const categoriesOptions = (t) => [
  { value: 'earthmoving', label: t('Earthmoving') },
  {
    value: 'elevation and scaffolding',
    label: t('Elevation_and_scaffolding')
  },
  { value: 'landscaping', label: t('Landscaping') },
  { value: 'handling', label: t('Handling') },
  { value: 'specialized tooling', label: t('Specialized_tooling') },
  { value: 'energy and air', label: t('Energy_and_air') },
  { value: 'waste solution', label: t('Waste_solution') },
  { value: 'fluid solutions', label: t('Fluid_solutions') },
  { value: 'site solution', label: t('Site_solution') },
  { value: 'event and reception', label: t('Event_reception') }
];

export const subCategoriesOptions = (t) => [
  { value: 'Boom lift', label: t('Boom_lift') },
  { value: 'Ladder and stepladder', label: t('Ladder_and_stepladder') },
  { value: 'Load lifting', label: t('Load_lifting') },
  { value: 'Manual scaffold', label: t('Manual_scaffold') },
  { value: 'Scissor lift', label: t('Scissor_lift') },
  { value: 'Telehandler', label: t('Telehandler') },
  { value: 'Work platform', label: t('Work_platform') },
  { value: 'Forklift', label: t('Forklift') },
  { value: 'Compaction', label: t('Compaction') },
  { value: 'Conveyor', label: t('Conveyor') },
  { value: 'Excavation', label: t('Excavation') },
  { value: 'Levels and measures', label: t('Levels_and_measures') },
  { value: 'Loader', label: t('Loader') },
  { value: 'Pallet Truck', label: t('Pallet_truck') },
  { value: 'Protection', label: t('Protection') },
  { value: 'Shoring post', label: t('Shoring_post') },
  { value: 'Trailer', label: t('Trailer') },
  { value: 'Wheelbarrow', label: t('Wheelbarrow') },
  { value: 'Heating', label: t('Heating') },
  { value: 'Pump', label: t('Pump') },
  { value: 'Ventilation', label: t('Ventilation') },
  { value: 'Generator', label: t('Generator') },
  { value: 'Green space maintenance', label: t('Green_space_maintenance') },
  { value: 'Trimming and pruning', label: t('Trimming_and_pruning') },
  { value: 'Cleaning', label: t('Cleaning') },
  { value: 'Electricity', label: t('Electricity') },
  { value: 'Plumbing', label: t('Plumbing') },
  { value: 'Roofing', label: t('Roofing') },
  { value: 'Welding', label: t('Welding') },
  { value: 'Concrete and masonry', label: t('Concrete_and_masonry') },
  { value: 'Ceramic', label: t('Ceramic') },
  { value: 'Gypsum', label: t('Gypsum') },
  { value: 'Wood', label: t('Wood') },
  { value: 'Carpet', label: t('Carpet') },
  { value: 'Metal and steel', label: t('Furniture') },
  {
    value: 'Dump cart',
    label: t('Dump_cart')
  },
  {
    value: 'Trash chute',
    label: t('Trash_chute')
  },
  {
    value: 'Cooking equipment',
    label: t('Cooking_equipment')
  },
  {
    value: 'Portable hand wash stations',
    label: t('Portable_hand_wash_stations')
  },
  {
    value: 'Portable restrooms',
    label: t('Portable_restrooms')
  },
  {
    value: 'Tool security box',
    label: t('Tool_security_box')
  },
  {
    value: 'Office trailer',
    label: t('Office_trailer')
  },
  {
    value: 'Barricade and sign',
    label: t('Barricade_and_sign')
  },
  {
    value: 'Roll off bins',
    label: t('Roll_off_bins')
  }
];

export const status = (t) => [
  {
    value: 'available',
    label: t('Available_text')
  },
  {
    value: 'booked',
    label: t('Booked')
  },
  {
    value: 'idle',
    label: t('Idle')
  }
];

export const isValid = (formik, attribute) =>
  (formik.touched[attribute] && formik.errors[attribute] && 'is-invalid') ||
  (formik.dirty && !formik.errors[attribute] && 'is-valid');

export const resetInsurance = (formik) => {
  formik.setFieldValue('insurance', {
    insurance_coverage: '',
    expiry_date_of_insurance: '',
    insurance_company: '',
    insurance_policy_number: ''
  });
  formik.setFieldError('insurance', {});
  formik.setFieldTouched('insurance', false);
  formik.isValid = true;
  formik.dirty = false;
};

export const attributes = [
  'type_of_propulsion',
  'width',
  'length',
  'height',
  'weight',
  'consumption',
  'capacity',
  'force',
  'volt',
  // 'kw',
  'btu',
  'brand',
  'brand_model',
  // 'drive_type',
  'diameter',
  'cut_diameter',
  'watt',
  'cfm',
  'platform_height',
  'working_height',
  'horizontal_outreach',
  'platform_capacity',
  'platform_dimension',
  'platform_extension',
  'extension_capacity',
  'platform_rotation',
  'machine_rotation',
  'machine_width',
  'machine_length',
  'machine_height',
  'closed_machine_height',
  'closed_machine_length',
  'closed_machine_width',
  'basket_capacity',
  'basket_length',
  'basket_width',
  'legs_location',
  'floor_height',
  'cabin_height',
  'wheelbase',
  'wheel_size',
  'plate_dimension',
  'decibel',
  'roll_width',
  'compaction',
  'vibrations',
  'lumen',
  'pressure',
  'frequency',
  'tilting_capacity',
  'operation_capacity',
  'tank_capacity',
  'digging_depth',
  'dumping_height',
  'digging_radius',
  'technical_data_sheet'
];

export const bidzAttributes = [
  'width',
  'length',
  'height',
  'weight',
  'consumption',
  'capacity',
  'force',
  'volt',
  // 'kw',
  'btu',
  'brand',
  'brand_model',
  // 'drive_type',
  'diameter',
  'cut_diameter',
  'watt',
  'cfm',
  'platform_height',
  'working_height',
  'horizontal_outreach',
  'platform_capacity',
  'platform_dimension',
  'platform_extension',
  'extension_capacity',
  'platform_rotation',
  'machine_rotation',
  'machine_width',
  'machine_length',
  'machine_height',
  'closed_machine_height',
  'closed_machine_length',
  'closed_machine_width',
  'basket_capacity',
  'basket_length',
  'basket_width',
  'legs_location',
  'floor_height',
  'cabin_height',
  'wheelbase',
  'wheel_size',
  'plate_dimension',
  'decibel',
  'roll_width',
  'compaction',
  'vibrations',
  'lumen',
  'pressure',
  'frequency',
  'tilting_capacity',
  'operation_capacity',
  'tank_capacity',
  'digging_depth',
  'dumping_height',
  'digging_radius',
  'technical_data_sheet'
];

export const specialPriceData = (data, t) => {
  if (data && data?.special_price) {
    return [
      {
        placeholder: t('Day'),
        value: data?.special_price?.day,
        currency: data?.special_price?.currency,
        name: 'day'
      },
      {
        placeholder: t('Week'),
        value: data?.special_price?.week,
        currency: data?.special_price?.currency,
        name: 'week'
      },
      {
        placeholder: t('Four_week'),
        value: data?.special_price?.month,
        currency: data?.special_price?.currency,
        name: 'month'
      }
    ];
  }
  return [];
};

export const onlyFilledItemsFilter = (item) => {
  return Object.keys(item).filter(
    (keyName) =>
      itemsToReturn(keyName) &&
      item[keyName] !== undefined &&
      item[keyName] !== '' &&
      item[keyName] !== 0
  );
};

export const carouselItems = [
   {
     src: Fero,
     alt: "Fero's logo",
     link: 'https://feronow.com/'
   },
  {
   src: WheelJackets,
     alt: "Wheel Jackets logo",
     link: 'https://wheeljackets.com/'
  },
  {
    src: CitationLogistics,
    alt: "CitationLogistics's logo",
    link: 'https://www.citationlogistics.com/'
  },
  {
    src: Biolift,
    alt: "biolift's logo",
    link: 'https://biolift.co/'
  },
  {
    src: FundingYourWay,
    alt: "FundingYourWay's logo",
    link: 'https://www.fundingyourway.biz'
  },
  {
    src: Ganahl,
    alt: "ganahl's logo",
    link: 'https://www.ganahllumber.com/'
  }
  /*   {
    src: Mazo,
    alt: "mazo's logo",
    link: 'https://mazocapital.com/'
  },
  {
    src: Strada,
    alt: "strada's logo",
    link: 'https://stradacapital.com/'
  } */
];

export function calculateDurationInDays(startTimestamp, endTimestamp) {
  const startDate = new Date(startTimestamp);
  const endDate = new Date(endTimestamp);

  startDate.setHours(0, 0, 0, 0);
  endDate.setHours(0, 0, 0, 0);

  const timeDifference = endDate.getTime() - startDate.getTime();

  const daysDifference = Math.ceil(timeDifference / (1000 * 60 * 60 * 24));

  return daysDifference + 1;
}

export function calculateRentalDuration(startDate, endDate, t) {
  const totalDays = calculateDurationInDays(startDate, endDate);
  const weeks = Math.floor(totalDays / 7);
  const days = totalDays % 7;

  let duration = '';

  if (weeks > 0) {
    duration += `${weeks} ${t('Week')}${weeks > 1 ? 's' : ''}`;
  }
  if (days > 0) {
    if (weeks > 0) {
      duration += ` ${t('And')} `;
    }
    duration += `${days} ${t('Day')}${days > 1 ? 's' : ''}`;
  }

  return duration;
}

export const itemsToReturnEquipmentDetails = (value) => {
  return ![
    'address',
    'name',
    'name_fr',
    'equipper_name',
    'equipper_email',
    'created_at',
    'image_link',
    'updated_at',
    '__position',
    '_highlightResult',
    'objectID',
    'lastmodified',
    'coverage_area',
    'equipper_id',
    'total_comments',
    'avg_rating',
    'path',
    'price',
    'id',
    'requested_name',
    'equipment_utility',
    'alias_id',
    'is_active',
    'equipper_equipment_picture'
  ].includes(value);
};

export function format(str) {
  if (typeof str !== 'string') {
    return str;
  }

  const index = str.indexOf('|||');
  if (index !== -1) {
    return str.slice(0, index);
  }

  return str;
}
