import React from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import { cutString } from '../../helpers/String_helps';
import CustomImage from '../images/Custom_image';
import CustomTooltip from '../tooltips/Tooltip';

export default function EquipperSpotlightEquipmentsItem({
  hit,
  isRecommendationsSection
}) {
  const { t, i18n } = useTranslation();
  const navigate = useNavigate();
  const name = hit.preferred_equipment_name
    ? hit.preferred_equipment_name
    : hit.name;
  const description =
    i18n.language === 'fr' ? hit.description_fr : hit.description;

  return (
    <div className="col-lg-4">
      <div
        className="similarProduct-box margin-bottom"
        style={{
          borderRadius: '12px',
          border: '1px solid #e5ecf6',
          background: '#ffffff',
          boxShadow: '0 4px 12px rgba(0,0,0,0.1)',
          transition: 'all 0.3s ease',
          overflow: 'hidden'
        }}
        onMouseEnter={(e) => {
          e.currentTarget.style.transform = 'translateY(-4px)';
          e.currentTarget.style.boxShadow = '0 8px 20px rgba(0,0,0,0.15)';
        }}
        onMouseLeave={(e) => {
          e.currentTarget.style.transform = 'translateY(0)';
          e.currentTarget.style.boxShadow = '0 4px 12px rgba(0,0,0,0.1)';
        }}
      >
        <div
          className="img-box"
          style={{ borderRadius: '12px 12px 0 0', overflow: 'hidden' }}
        >
          <CustomImage
            imageUrl={
              hit.equipper_equipment_picture &&
              !hit.equipper_equipment_picture.includes(
                'equipment_library/Empty_state_equipment.png'
              ) &&
              !isRecommendationsSection
                ? hit.equipper_equipment_picture
                : hit.image_link
            }
            style={{
              width: '100%',
              height: '200px',
              objectFit: 'cover',
              transition: 'transform 0.3s ease'
            }}
          />
        </div>
        <div className="content-box" style={{ padding: '20px' }}>
          <h3
            className="t-body-large c-fake-black bold"
            style={{
              fontSize: '1.2rem',
              fontWeight: '700',
              color: '#1f2937',
              marginBottom: '12px',
              letterSpacing: '0.5px'
            }}
          >
            {cutString(name, 20)}
          </h3>
          <CustomTooltip text={description || 'N/A'} placement="bottom">
            <p className="t-body-small c-neutrals-gray">
              <strong className="t-body-small c-neutrals-gray bold">
                {t('Description')} :{' '}
              </strong>
              {description === '' || description === null
                ? t('Not_specified')
                : cutString(description, 15)}
            </p>
          </CustomTooltip>
          <div className="button-content mt-3">
            <button
              className="round-button blue bold c-white d-inline-flex align-items-center justify-content-center view-details-btn hover_black"
              onClick={() => {
                console.log('EquipperSpotlightEquipmentsItem hit.objectID: ---->', hit.objectID);
                console.log('EquipperSpotlightEquipmentsItem hit.equipper_id:----->', hit.equipper_id);
                navigate(`/equipmentDetails/${hit.objectID}_${hit.equipper_id}`);
              }}
              style={{
                fontSize: '0.875rem',
                padding: '8px 16px',
                borderRadius: '20px',
                border: 'none',
                background: '#0a2347db',
                color: 'white',
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                boxShadow: '0 2px 8px rgba(6, 28, 61, 0.3)',
                fontWeight: '600',
                letterSpacing: '0.5px'
              }}
              onMouseEnter={(e) => {
                e.target.style.transform = 'translateY(-2px)';
                e.target.style.boxShadow = '0 4px 12px rgba(6, 28, 61, 0.4)';
              }}
              onMouseLeave={(e) => {
                e.target.style.transform = 'translateY(0)';
                e.target.style.boxShadow = '0 2px 8px rgba(6, 28, 61, 0.3)';
              }}
            >
              {t('View_details')}
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}
